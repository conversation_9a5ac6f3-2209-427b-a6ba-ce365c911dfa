#ifndef INTERRUPT_H
#define INTERRUPT_H

#include "mydefine.h"

extern uint8_t task2_first_flag;
extern uint8_t step_x_flag;  // X轴电机到位标志
extern uint8_t step_y_flag;  // Y轴电机到位标志

//主状态   
#define  STATE_IDLE   0
#define  TASK_2       1
#define  TASK_3       2
#define  TASK_4       3

//任务2子状态
#define task2_state0 0
#define task2_state1 1
#define task2_state2 2
#define task2_state3 3
#define task2_state4 4
#define task2_state5 5
#define task2_state6 6
#define task2_state7 7
#define task2_state8 8

//任务2子状态
#define task3_state0 0
#define task3_state1 1
#define task3_state2 2
#define task3_state3 3

//任务4子状态
#define task4_state0 0
#define task4_state1 1
#define task4_state2 2
#define task4_state3 3


// 主状态结构体def
struct state_machine
{
   int MAIN_STATE;   	  // 空闲状态
   int STATE_TASK2;       // 第二问状态
   int STATE_TASK3;       // 第三问状态
   int STATE_TASK4;       // 第四问状态
};
extern uint32_t state_start_time; 
void State_Machine_init(void);
extern struct state_machine State_Machine;
extern uint16_t arrive_flag;
void init_four_corner_compensation(void);
#endif
