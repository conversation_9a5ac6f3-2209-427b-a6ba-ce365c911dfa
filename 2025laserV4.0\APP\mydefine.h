#ifndef __MYDEFINE_H_
#define __MYDEFINE_H_

#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <math.h>
#include <stdint.h>
#include <stdbool.h>

#include "main.h"
#include "usart.h"
#include "gpio.h"
#include "spi.h"

#include "uart.h"
#include "Emm_V5.h"
#include "scheduler.h"
#include "usart_app.h"
#include "interrupt.h"
#include "pid.h"
#include "app_motor.h"
#include "app_Point2D.h"
#include "Relay.h"
#include "W25Q64.h"
#include "hwt101.h"
#include "hwt101_driver.h"
#include "ringbuffer.h"

extern int32_t num1;
extern int32_t num2;

extern DMA_HandleTypeDef hdma_uart4_rx;
#endif
