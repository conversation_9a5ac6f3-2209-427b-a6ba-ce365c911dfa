#include "pid.h"
#include "math.h"

tPid motorA_pid;
tPid motorB_pid;

//给结构体类型变量赋初值
void PID_init()
{
    motorA_pid.actual_val=0.0;
    motorA_pid.target_val=0.00;
    motorA_pid.err=0.0;
    motorA_pid.err_last=0.0;
    motorA_pid.err_sum=0.0;
    motorA_pid.Kp=0.5;
    motorA_pid.Ki=0.0;
    motorA_pid.Kd=1.5;
    motorA_pid.output=0.0;
    
    motorB_pid.actual_val=0.0;
    motorB_pid.target_val=0.00;
    motorB_pid.err=0.0;
    motorB_pid.err_last=0.0;
    motorB_pid.err_sum=0.0;
    motorB_pid.Kp=0.5;
    motorB_pid.Ki=0.0;
    motorB_pid.Kd=1.5;
    motorB_pid.output=0.0;
}
void TASK4_PID_init()
{
    motorA_pid.actual_val=0.0;
    motorA_pid.target_val=0.00;
    motorA_pid.err=0.0;
    motorA_pid.err_last=0.0;
    motorA_pid.err_sum=0.0;
    motorA_pid.Kp=0.65;
    motorA_pid.Ki=0.0;
    motorA_pid.Kd=1.6;
    motorA_pid.output=0.0;
    
    motorB_pid.actual_val=0.0;
    motorB_pid.target_val=0.00;
    motorB_pid.err=0.0;
    motorB_pid.err_last=0.0;
    motorB_pid.err_sum=0.0;
    motorB_pid.Kp=0.5;
    motorB_pid.Ki=0.0;
    motorB_pid.Kd=1.5;
    motorB_pid.output=0.0;
}
 //PID控制函数
float PID_realize(tPid * pid,float err)
{
	pid->err = err;

	pid->err_sum += pid->err;//误差累计值 = 当前误差累计和
	pid->output = pid->Kp * pid->err                // 比例项
                + pid->Ki * pid->err_sum             // 积分项
                + pid->Kd * (pid->err - pid->err_last); // 微分项
	
	if(motorA_pid.err < 5 && motorA_pid.err > -5 && motorB_pid.err < 5 && motorB_pid.err > -5) arrive_flag = 1;
	
	if(pid->err_sum > 400.0f) pid->err_sum = 400.0f;
	else if(pid->err_sum < -400.0f) pid->err_sum = -400.0f;

	if(pid->output > 80.0f) pid->output = 80.0f;
	else if(pid->output < -80.0f) pid->output = -80.0f;
	
	pid->err_last = pid->err;
	return pid->output;
}

void track_control(void)
{
	//motorA_xiangdui(PID_realize(&motorA_pid,camera_x_error));
	//motorB_xiangdui(PID_realize(&motorB_pid,camera_y_error));
	my_printf(&huart1,"%.3f,%.3f\r\n",motorA_pid.err,motorA_pid.output);
	Motor_Set_Speed(-PID_realize(&motorA_pid,(float)camera_x_error),-PID_realize(&motorB_pid,(float)camera_y_error));
	//my_printf(&huart1,"%.3f,%.3f\r\n",motorA_pid.output,motorB_pid.output);
}

// TASK4专用自适应PID控制函数
float TASK4_Adaptive_PID(tPid * pid, float err)
{
    pid->err = err;
    float abs_err = fabs(err);

    // TASK4自适应参数调节 - 只优化X轴
    if(pid == &motorA_pid) {
        // X轴自适应控制
        if(abs_err > 50.0f) {
            // 大误差：轻微提高响应速度
            pid->Kp = 0.85;  // 轻微提高（约1.3倍）
            pid->Kd = 1.8;   // 轻微增强阻尼
        } else if(abs_err > 20.0f) {
            // 中等误差：渐变参数
            float ratio = (abs_err - 20.0f) / 30.0f;
            pid->Kp = 0.65 + ratio * (0.85 - 0.65);
            pid->Kd = 1.6 + ratio * (1.8 - 1.6);
        } else {
            // 小误差：使用原始参数
            pid->Kp = 0.65;
            pid->Kd = 1.6;
        }
    } else {
        // Y轴保持原始TASK4参数不变
        pid->Kp = 0.5;
        pid->Kd = 1.5;
    }

    // 计算PID输出
    pid->err_sum += pid->err;
    pid->output = pid->Kp * pid->err +
                  pid->Ki * pid->err_sum +
                  pid->Kd * (pid->err - pid->err_last);

    // 到位判断
    if(fabs(motorA_pid.err) < 5 && fabs(motorB_pid.err) < 5)
        arrive_flag = 1;

    // 积分限幅
    if(pid->err_sum > 400.0f) pid->err_sum = 400.0f;
    else if(pid->err_sum < -400.0f) pid->err_sum = -400.0f;

    // 输出限幅
    if(pid->output > 80.0f) pid->output = 80.0f;
    else if(pid->output < -80.0f) pid->output = -80.0f;

    pid->err_last = pid->err;
    return pid->output;
}

// TASK4专用追踪控制函数
void task4_track_control(void)
{
    float x_output = TASK4_Adaptive_PID(&motorA_pid, (float)camera_x_error);
    float y_output = TASK4_Adaptive_PID(&motorB_pid, (float)camera_y_error);

//    my_printf(&huart1,"T4_X:%.1f->%.1f,Y:%.1f->%.1f\r\n",
//              motorA_pid.err, x_output, motorB_pid.err, y_output);

    Motor_Set_Speed(-x_output, -y_output);
}

	
