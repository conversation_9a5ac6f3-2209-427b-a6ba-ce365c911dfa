#include "interrupt.h"

#define STATE_DELAY_MS 200  // 状态延迟时间200ms
#define WAIT_DELAY_MS 200  // 等待到位延迟时间200ms

struct state_machine State_Machine;
uint16_t task2_count;
uint16_t task3_count;
uint32_t state_start_time = 0;  // 状态开始时间戳
uint16_t arrive_flag;
void task2_proc(void);
void task3_proc(void);
void task4_proc(void);
int16_t count1;
int16_t count2;

void State_Machine_init()
{
    State_Machine.MAIN_STATE = STATE_IDLE;
    State_Machine.STATE_TASK2 = task2_state0;
    State_Machine.STATE_TASK3 = task3_state0;
    State_Machine.STATE_TASK4 = STATE_IDLE;//没改
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
	if(htim -> Instance == TIM6)//20ms进入一次中断
	{
		switch(State_Machine.MAIN_STATE)
		{
			case STATE_IDLE://IDLE
				
			break;
			case TASK_2://第二问逻辑
			{
				task2_proc();
			}
			break;
			case TASK_3: //第三问逻辑
			{
				task3_proc();
			}
			break;
			case TASK_4: //第四问逻辑
			{
				task4_proc();
			}
			break;
			default:
				
			break;
		}
		
	}
	
}
void task3_proc()
{
	
	switch(State_Machine.STATE_TASK3)
	{
		case task3_state0:
			Relay(0);
			if(camera_x_error == 0 &&  camera_y_error == 0)
				State_Machine.STATE_TASK3 = task3_state1;
			else 
				State_Machine.STATE_TASK3 = task3_state2;
		break;
		case task3_state1:
			Auto_find();
		break;
		case task3_state2:
			track_control();
			if(arrive_flag) 
			{
				if(++task3_count == 20)
				{
					task3_count = 0;
					arrive_flag = 0;
					State_Machine.STATE_TASK3 = task3_state3;
				}
			}
		break;
		case task3_state3:
			Relay(1);
			Motor_Set_Speed(0,0);
			//track_control();
			//State_Machine.STATE_TASK3 = task3_state0;
			//State_Machine.MAIN_STATE = STATE_IDLE;
		break;
	}
}
void task2_proc()
{	
	switch(State_Machine.STATE_TASK2)
	{
		case task2_state0: //立即定位
			Relay(0);
			motorB_pos_control(num2,200);
			motorA_pos_control(num1,200);	
			State_Machine.STATE_TASK2 = task2_state1;
		break;
		case task2_state1: //等待1s的关闭激光
			if(++task2_count == 50)
			{
				task2_count = 0;
				State_Machine.STATE_TASK2 = task2_state2;
			}
		break;
		case task2_state2: //开启激光 - 0.5s
			Relay(1);
			State_Machine.MAIN_STATE = STATE_IDLE;
			State_Machine.STATE_TASK2 = task2_state0;	
	}
}

void task4_proc()
{
	switch(State_Machine.STATE_TASK4)
	{
		case task4_state0:  // 复用task3的状态定义
			Relay(0);
			if(camera_x_error == 0 &&  camera_y_error == 0)
				State_Machine.STATE_TASK4 = task4_state1;
			else
				State_Machine.STATE_TASK4 = task4_state2;
		break;
		case task4_state1:
			Auto_find_task4();
		break;
		case task4_state2:
			task4_track_control();  // 使用TASK4专用自适应PID
			if(arrive_flag)
			{
				if(++task3_count == 10)
				{
					task3_count = 0;
					arrive_flag = 0;
					State_Machine.STATE_TASK4 = task4_state3;
				}
			}
		break;
		case task4_state3:
			Relay(1);
			task4_track_control();  // 使用TASK4专用自适应PID
		break;
	}
}
