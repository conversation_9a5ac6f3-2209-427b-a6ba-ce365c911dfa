# 前馈控制技术知识库总结

## 核心概念

### 什么是前馈控制？
前馈控制是一种**预测性控制方法**，通过提前检测扰动并预先补偿，避免等待误差产生后再纠正。

**生活类比**：
- **无前馈**：等车身偏离才打方向盘（纯PID）
- **有前馈**：看到弯道提前打方向盘（前馈控制）

**云台系统中**：
- **无前馈**：激光偏离→PID检测→纠正（有延迟抖动）
- **有前馈**：IMU检测小车运动→立即补偿→激光稳定

## 物理原理

### 小车运动对云台的影响
1. **直线加速/减速**：小车加速→云台惯性后仰→激光点上移
2. **转弯运动**：小车左转→云台惯性右偏→激光点右移  
3. **离心力**：高速转弯→离心力外甩→激光点偏移

### 控制策略对比
```
传统PID：误差 → 控制
前馈控制：扰动 → 预测 → 补偿
```

## IMU数据解析

### 传感器数据
- **加速度计**：(ax, ay, az) 单位：m/s²
- **陀螺仪**：(wx, wy, wz) 单位：rad/s

### 坐标系定义
- **ax**：前后加速度（前进为正）
- **ay**：左右加速度（左为正）  
- **az**：上下加速度（向上为正）
- **wx**：翻滚角速度（绕X轴）
- **wy**：俯仰角速度（绕Y轴）
- **wz**：偏航角速度（绕Z轴，左转为正）

## 补偿算法

### 1. 直线运动补偿
```c
// 俯仰补偿：小车加速时云台后仰
pitch_compensation = ax * K_pitch;
```

### 2. 转弯补偿  
```c
// 偏航补偿：小车转弯时云台反向偏转
yaw_compensation = wz * height * K_yaw;
```

### 3. 离心力补偿
```c
// 离心加速度补偿
centrifugal_acc = velocity * wz;
```

## 实现流程

### 数据预处理
```c
// 1. 读取原始数据
read_imu_data(&ax, &ay, &az, &wx, &wy, &wz);

// 2. 去除静态偏置
ax -= ax_bias; ay -= ay_bias; az -= az_bias;
wx -= wx_bias; wy -= wy_bias; wz -= wz_bias;
```

### 运动状态识别
- 检测加速/减速状态
- 识别转弯动作
- 计算运动强度

### 补偿量计算
- 基于物理模型计算补偿角度
- 应用滤波减少噪声
- 限制补偿量范围

### 控制应用
```c
// 前馈+PID组合控制
gimbal_pitch = pid_pitch + pitch_comp;
gimbal_yaw = pid_yaw + yaw_comp;
```

## 调试策略

### 调试顺序
1. **先调PID**：确保静止时PID稳定工作
2. **标定IMU**：记录静止时偏置值
3. **调直线补偿**：只测试加速/减速
4. **调转弯补偿**：固定速度转弯测试
5. **综合测试**：完整轨迹验证

### 参数调试
- **K_pitch**：从小开始，观察激光点稳定性
- **K_yaw**：根据云台高度和转弯效果调整

### 常见问题
- **补偿后更抖**：IMU噪声大，需加强滤波
- **转弯仍抖动**：检查IMU安装，增加死区
- **有延迟**：提高控制频率(≥100Hz)，减少滤波延迟

## 核心优势

1. **预测性**：提前检测扰动，主动补偿
2. **快速响应**：几乎无延迟的补偿效果
3. **减少抖动**：显著改善激光点稳定性
4. **协同工作**：与PID配合，处理可预测扰动

## 关键要点

> **前馈控制的本质**：
> - 预测扰动：通过IMU提前知道小车运动
> - 模型补偿：根据物理模型计算补偿量  
> - 快速响应：在扰动影响云台前就补偿
> 
> **重要**：前馈不替代PID，而是辅助PID。前馈处理可预测扰动，PID处理剩余误差。
