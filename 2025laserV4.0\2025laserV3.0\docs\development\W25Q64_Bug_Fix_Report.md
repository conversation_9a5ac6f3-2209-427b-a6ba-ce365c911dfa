# W25Q64模块数据耦合问题修复报告

## 问题描述

用户反映使用W25Q64模块时出现数据耦合问题：
- 第一次存储和读取数据正确
- 第二次存储后数据出现错误
- 数据相互覆盖或损坏

## 根本原因分析

经过深入分析，发现原有W25Q64模块存在严重的**地址处理错误**，导致数据写入到错误的Flash地址位置。

### 具体问题

#### 1. 扇区擦除函数地址错误
**原有代码（错误）：**
```c
void W25QXX_Erase_Sector(uint32_t sector_addr)
{
    sector_addr *= 4096;    // 正确：转换为字节地址
    sector_addr <<= 8;      // 错误：又左移8位，相当于乘以256
    // ...
}
```

**问题分析：**
- 实际擦除地址 = `sector_addr * 4096 * 256 = sector_addr * 1,048,576`
- 扇区0应该擦除地址0x0000，实际擦除地址0x000000
- 扇区1应该擦除地址0x1000，实际擦除地址0x100000
- 扇区2应该擦除地址0x2000，实际擦除地址0x200000

这导致不同扇区的数据相互覆盖！

#### 2. 读取函数地址错误
**原有代码（错误）：**
```c
int W25QXX_Read(uint8_t* buffer, uint32_t start_addr, uint16_t nbytes)
{
    start_addr = start_addr << 8;  // 错误：地址左移8位
    // ...
}
```

#### 3. 页编程函数地址错误
**原有代码（错误）：**
```c
void W25QXX_Page_Program(uint8_t* dat, uint32_t WriteAddr, uint16_t nbytes)
{
    WriteAddr <<= 8;  // 错误：地址左移8位
    // ...
}
```

## 修复方案

### 1. 修复扇区擦除函数
```c
void W25QXX_Erase_Sector(uint32_t sector_addr)
{
    uint8_t cmd = SECTOR_ERASE_CMD;
    uint32_t erase_addr;
    
    // 正确的地址计算
    erase_addr = sector_addr * 4096;
    
    // 正确的24位地址发送
    uint8_t addr_bytes[3];
    addr_bytes[0] = (uint8_t)((erase_addr >> 16) & 0xFF);
    addr_bytes[1] = (uint8_t)((erase_addr >> 8) & 0xFF);
    addr_bytes[2] = (uint8_t)(erase_addr & 0xFF);
    
    SPI_Transmit(addr_bytes, 3);
}
```

### 2. 修复读取函数
```c
int W25QXX_Read(uint8_t* buffer, uint32_t start_addr, uint16_t nbytes)
{
    // 移除错误的地址左移
    // start_addr = start_addr << 8;  // 删除此行
    
    // 正确的24位地址发送
    uint8_t addr_bytes[3];
    addr_bytes[0] = (uint8_t)((start_addr >> 16) & 0xFF);
    addr_bytes[1] = (uint8_t)((start_addr >> 8) & 0xFF);
    addr_bytes[2] = (uint8_t)(start_addr & 0xFF);
    
    SPI_Transmit(addr_bytes, 3);
}
```

### 3. 修复页编程函数
```c
void W25QXX_Page_Program(uint8_t* dat, uint32_t WriteAddr, uint16_t nbytes)
{
    // 移除错误的地址左移
    // WriteAddr <<= 8;  // 删除此行
    
    // 正确的24位地址发送
    uint8_t addr_bytes[3];
    addr_bytes[0] = (uint8_t)((WriteAddr >> 16) & 0xFF);
    addr_bytes[1] = (uint8_t)((WriteAddr >> 8) & 0xFF);
    addr_bytes[2] = (uint8_t)(WriteAddr & 0xFF);
    
    SPI_Transmit(addr_bytes, 3);
}
```

## 修复效果

### 修复前
- 扇区0实际操作地址：0x000000 ✓
- 扇区1实际操作地址：0x100000 ✗ (应该是0x001000)
- 扇区2实际操作地址：0x200000 ✗ (应该是0x002000)

### 修复后
- 扇区0实际操作地址：0x000000 ✓
- 扇区1实际操作地址：0x001000 ✓
- 扇区2实际操作地址：0x002000 ✓

## 验证建议

建议使用以下测试代码验证修复效果：

```c
void test_w25q64_fix(void)
{
    int32_t test_data1[3] = {111, 222, 333};
    int32_t test_data2[3] = {444, 555, 666};
    int32_t read_data1, read_data2;
    
    // 测试连续扇区写入
    for(int i = 0; i < 3; i++) {
        printf("写入扇区%d: data1=%ld, data2=%ld\n", i, test_data1[i], test_data2[i]);
        W25QXX_Write_Int32_Pair(test_data1[i], test_data2[i], i);
    }
    
    // 测试连续扇区读取
    for(int i = 0; i < 3; i++) {
        W25QXX_Read_Int32_Pair(&read_data1, &read_data2, i);
        printf("读取扇区%d: data1=%ld, data2=%ld\n", i, read_data1, read_data2);
        
        if(read_data1 == test_data1[i] && read_data2 == test_data2[i]) {
            printf("扇区%d验证成功！\n", i);
        } else {
            printf("扇区%d验证失败！\n", i);
        }
    }
}
```

## 总结

此次修复解决了W25Q64模块的核心地址处理问题，确保：
1. 数据写入到正确的Flash地址
2. 不同扇区的数据不会相互覆盖
3. 读写操作的地址一致性
4. 符合W25Q64BV数据手册的地址格式要求

修复后，您的int32_t数据读写函数将能够正常工作，不再出现数据耦合问题。
