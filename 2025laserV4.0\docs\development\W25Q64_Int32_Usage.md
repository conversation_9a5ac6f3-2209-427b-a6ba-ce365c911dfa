# W25Q64 Int32数据读写函数使用指南

## 概述

基于现有的W25Q64模块，新增了两个专门用于处理两个带符号int32_t数据的读写函数：
- `W25QXX_Write_Int32_Pair()` - 写入两个int32_t数据
- `W25QXX_Read_Int32_Pair()` - 读取两个int32_t数据

## 函数说明

### 写入函数
```c
int W25QXX_Write_Int32_Pair(int32_t data1, int32_t data2, uint32_t sector_addr);
```

**参数：**
- `data1`: 第一个int32_t数据
- `data2`: 第二个int32_t数据  
- `sector_addr`: 扇区地址(0-2047)

**返回值：**
- 0: 成功
- -1: 失败

**注意事项：**
- 会先擦除整个扇区(4KB)，然后写入8字节数据到扇区起始位置
- 扇区地址范围：0-2047 (W25Q64总共2048个扇区)
- 数据以小端序格式存储

### 读取函数
```c
int W25QXX_Read_Int32_Pair(int32_t* data1, int32_t* data2, uint32_t sector_addr);
```

**参数：**
- `data1`: 指向第一个int32_t数据的指针
- `data2`: 指向第二个int32_t数据的指针
- `sector_addr`: 扇区地址(0-2047)

**返回值：**
- 0: 成功
- -1: 失败

## 使用示例

```c
#include "W25Q64.h"

void example_usage(void)
{
    int32_t write_data1 = -123456;
    int32_t write_data2 = 987654;
    int32_t read_data1, read_data2;
    uint32_t sector = 100; // 使用第100个扇区
    
    // 写入数据
    if(W25QXX_Write_Int32_Pair(write_data1, write_data2, sector) == 0) {
        printf("数据写入成功\n");
    } else {
        printf("数据写入失败\n");
        return;
    }
    
    // 读取数据
    if(W25QXX_Read_Int32_Pair(&read_data1, &read_data2, sector) == 0) {
        printf("数据读取成功\n");
        printf("data1: %ld, data2: %ld\n", read_data1, read_data2);
        
        // 验证数据
        if(read_data1 == write_data1 && read_data2 == write_data2) {
            printf("数据验证成功！\n");
        } else {
            printf("数据验证失败！\n");
        }
    } else {
        printf("数据读取失败\n");
    }
}
```

## 技术细节

### 数据存储格式
- 每个int32_t占用4字节
- 两个int32_t总共占用8字节
- 采用小端序存储格式
- 数据存储在扇区的起始位置

### 地址计算
- 扇区地址 × 4096 = 实际Flash地址
- 例如：扇区100 → 地址0x64000

### 性能考虑
- 每次写入都会擦除整个4KB扇区
- 擦除时间约20-200ms
- 写入时间约0.7ms
- 读取时间约0.1ms

## 注意事项

1. **扇区擦除影响**：写入操作会擦除整个4KB扇区，如果扇区中有其他重要数据会被清除
2. **地址范围**：确保扇区地址在0-2047范围内
3. **指针有效性**：读取函数会检查指针是否为NULL
4. **错误处理**：建议在实际使用中添加重试机制
5. **并发访问**：如果有多线程访问，需要添加互斥锁保护

## 修复的原有问题

在封装过程中修复了原有W25Q64模块的地址处理问题：
- 原代码：`start_addr = start_addr << 8;` (错误)
- 新代码：正确的24位地址处理，高字节在前发送
