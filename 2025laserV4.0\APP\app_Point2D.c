#include "app_Point2D.h"

/**
 * 生成三角形外边框点阵函数
 * 
 * @param pulse_tl_x    左上角X轴脉冲值
 * @param pulse_tl_y    左上角Y轴脉冲值  
 * @param pulse_br_x    右下角X轴脉冲值
 * @param pulse_br_y    右下角Y轴脉冲值
 * @param total_points  点阵总数量
 * @param points_array  保存点阵的二维数组 [total_points][2]
 * @return             成功返回0，失败返回-1
 */
int generate_triangle_grid(int pulse_tl_x, int pulse_tl_y, 
                          int pulse_br_x, int pulse_br_y,
                          int total_points, int points_array[][2]) {
    
    // 参数验证
    if (total_points < 3 || points_array == NULL) {
        return -1;
    }
    
    // 计算坐标系尺寸
    int width = abs(pulse_br_x - pulse_tl_x);
    int height = abs(pulse_br_y - pulse_tl_y);
    
    if (width == 0 || height == 0) {
        return -1;
    }
    
    // 计算坐标系中心
    int center_x = pulse_tl_x + width / 2;
    int center_y = pulse_tl_y + height / 2;
    
    // 计算三角形大小（占坐标系的60%）
    int min_dimension = (width < height ? width : height);
    int tri_size = (min_dimension * 6) / 10;  // 60% 用整数运算
    int tri_height = (tri_size * 866) / 1000; // 0.866 用整数运算
    
    // 添加调试信息
    #ifdef DEBUG_TRIANGLE
    my_printf(&huart1, "Debug: width=%d, height=%d, tri_size=%d, tri_height=%d\n",
              width, height, tri_size, tri_height);
    my_printf(&huart1, "Debug: center=(%d,%d)\n", center_x, center_y);
    #endif

    // 计算三角形三个顶点坐标
    int vertex1_x = center_x;
    int vertex1_y = center_y - tri_height / 2;  // 顶点

    int vertex2_x = center_x - tri_size / 2;
    int vertex2_y = center_y + tri_height / 2;  // 左下

    int vertex3_x = center_x + tri_size / 2;
    int vertex3_y = center_y + tri_height / 2;  // 右下
    
    // 计算每条边的点数（均匀分配）
    int points_per_edge = total_points / 3;
    int remaining_points = total_points % 3;
    
    int point_index = 0;
    
    // 生成三条边的点
    for (int edge = 0; edge < 3; edge++) {
        int start_x, start_y, end_x, end_y;
        int edge_points = points_per_edge;
        
        // 将余数分配给前几条边
        if (edge < remaining_points) {
            edge_points++;
        }
        
        // 确定当前边的起点和终点
        switch (edge) {
            case 0: // 第一条边：顶点到左下
                start_x = vertex1_x; start_y = vertex1_y;
                end_x = vertex2_x; end_y = vertex2_y;
                break;
            case 1: // 第二条边：左下到右下
                start_x = vertex2_x; start_y = vertex2_y;
                end_x = vertex3_x; end_y = vertex3_y;
                break;
            case 2: // 第三条边：右下到顶点
                start_x = vertex3_x; start_y = vertex3_y;
                end_x = vertex1_x; end_y = vertex1_y;
                break;
        }
        
        // 在当前边上生成点
        for (int i = 0; i < edge_points && point_index < total_points; i++) {
            float t;
            if (edge_points == 1) {
                t = 0.5; // 只有一个点时放在中间
            } else {
                t = (float)i / (edge_points - 1);
            }
            
            // 线性插值计算点坐标
            int point_x = (int)(start_x + t * (end_x - start_x));
            int point_y = (int)(start_y + t * (end_y - start_y));
            
            // 存储到数组
            points_array[point_index][0] = point_x;
            points_array[point_index][1] = point_y;
            point_index++;
        }
    }
    
    return 0; // 成功
}

// 辅助函数：打印点阵（用于调试）
void print_triangle_grid(int points_array[][2], int total_points) {
    //my_printf(&huart1,"生成的三角形点阵坐标：\n");
    for (int i = 0; i < total_points; i++) {
        my_printf(&huart1, "%d: (%d, %d)\n", i+1, points_array[i][0], points_array[i][1]);
    }
}

void point2D_init()
{
    // 测试用例：使用你提供的实际数据
    // 四个顶点：(211, 260), (-33, 252), (-24, 103), (218, 111)
    int pulse_tl_x = -33, pulse_tl_y = 103;  // 左上角 (min_x, min_y)
    int pulse_br_x = 218, pulse_br_y = 260;  // 右下角 (max_x, max_y)

    int test_points = 10;  // 测试用少量点数
    int test_grid[10][2];  // 存储点阵的数组

    // 生成三角形点阵
    int result = generate_triangle_grid(pulse_tl_x, pulse_tl_y,
                                       pulse_br_x, pulse_br_y,
                                       test_points, test_grid);

    if (result == 0) {
        my_printf(&huart1, "Test triangle grid:\n");
        print_triangle_grid(test_grid, test_points);
    } else {
        my_printf(&huart1, "Test triangle generation failed!\n");
    }
}
