# Task4proc2功能分析报告

## 概述

`task4_proc2()` 是您工程中第四问（TASK_4）的核心控制函数，实现了一个**混合控制系统**，结合了**视觉跟踪控制**和**IMU四拐点补偿控制**，用于激光云台的精确控制。

## 功能架构

### 1. 调用层次结构
```
HAL_TIM_PeriodElapsedCallback (TIM6中断，20ms周期)
└── task4_proc2() [当 State_Machine.MAIN_STATE == TASK_4 时]
    ├── task4_state0: 初始状态判断
    ├── task4_state1: 自动搜索模式
    ├── task4_state2: 视觉跟踪模式  
    └── task4_state3: 混合控制模式 (视觉+IMU四拐点补偿)
```

### 2. 状态机设计

#### task4_state0 - 初始状态
- **功能**: 关闭激光，判断视觉系统状态
- **逻辑**: 
  - 如果摄像头误差为0 (`camera_x_error == 0 && camera_y_error == 0`)，进入自动搜索模式
  - 否则进入视觉跟踪模式

#### task4_state1 - 自动搜索模式
- **功能**: 调用 `Auto_find_task4()` 进行目标搜索
- **实现**: 以固定速度移动，直到检测到目标后切换到跟踪模式

#### task4_state2 - 视觉跟踪模式
- **功能**: 使用专用自适应PID进行视觉跟踪
- **核心函数**: `task4_track_control()`
- **到位判断**: 当 `arrive_flag` 置位且持续10个周期(200ms)后，进入混合控制模式

#### task4_state3 - 混合控制模式 ⭐核心创新⭐
- **功能**: 开启激光，实现视觉控制与IMU四拐点补偿的智能切换
- **核心算法**:
  ```c
  bool in_dead_zone = is_in_dead_zone(hwt_yaw);
  
  if(in_dead_zone) {
      // 在死区使用视觉控制
      task4_track_control();
  } else {
      // 在非死区使用四拐点IMU控制
      imu_four_corner_control(hwt_yaw, last_yaw, in_dead_zone);
  }
  ```

## 核心技术特性

### 1. 死区检测算法
```c
bool is_in_dead_zone(float yaw_360) {
    return (yaw_360 >= 355.0f || yaw_360 <= 5.0f) ||      // AB段 (0°±5°)
           (yaw_360 >= 85.0f && yaw_360 <= 95.0f) ||      // BC段 (90°±5°)
           (yaw_360 >=175.0f && yaw_360 <= 195.0f) ||     // CD段 (180°±5°)
           (yaw_360 >= 265.0f && yaw_360 <= 285.0f);      // DA段 (270°±5°)
}
```
- **作用**: 识别四个拐点附近的±5°死区
- **意义**: 在拐点附近IMU数据可能不稳定，切换到视觉控制确保精度

### 2. 四拐点IMU补偿系统

#### 补偿数据结构
```c
// 四拐点云台补偿角度数组
float gimbal_compensation_angles[4] = {
    114.1f,  // 第1个拐点：车转90°，云台转114.1°
    70.0f,   // 第2个拐点
    78.9f,   // 第3个拐点  
    86.5f    // 第4个拐点
};

// 四拐点对应的车辆转动角度
float car_rotation_angles[4] = {90.0f, 90.0f, 90.0f, 90.0f};
```

#### 补偿算法核心
```c
// 计算当前拐点的补偿比例
float compensation_ratio = gimbal_compensation_angles[current_corner] / car_rotation_angles[current_corner];

// 计算云台需要转动的角度
float gimbal_delta = delta_yaw * compensation_ratio;

// 转换为脉冲数 (1脉冲 = 0.007度)
int32_t x_pulse = (int32_t)(gimbal_delta / 0.007f + 0.5f);
```

#### 拐点切换逻辑
- **触发条件**: 刚离开死区时 (`last_in_dead_zone && !in_dead_zone`)
- **切换方式**: `current_corner = (current_corner + 1) % 4`
- **首次运行保护**: 避免启动时误切换

### 3. 自适应PID控制
- **函数**: `task4_track_control()` 调用 `TASK4_Adaptive_PID()`
- **特点**: 针对TASK4优化的PID参数，提供更好的跟踪性能

## 数据流分析

### 输入数据源
1. **IMU数据**: `hwt_yaw` (0-360°范围的偏航角)
2. **视觉数据**: `camera_x_error`, `camera_y_error` (摄像头误差值)
3. **状态标志**: `arrive_flag` (到位标志)

### 输出控制
1. **激光控制**: `Relay(0/1)` 控制激光开关
2. **电机控制**: 
   - `motorA_xiangdui()` - X轴云台相对位置控制
   - `Motor_Set_Speed()` - XY轴速度控制

### 调试输出
- 拐点切换信息: `"conner:%d，angle:%.1f°"`
- IMU控制详情: `"A%d: B%.2f° C%.2f° D%ld"`

## 技术优势

### 1. 混合控制策略
- **视觉控制**: 在死区提供精确定位
- **IMU控制**: 在非死区提供快速响应和预测性补偿
- **无缝切换**: 根据角度位置智能选择控制方式

### 2. 四拐点补偿
- **个性化补偿**: 每个拐点都有独立的补偿参数
- **物理建模**: 基于车辆转动角度和云台补偿角度的比例关系
- **高精度转换**: 0.007°/脉冲的精确控制

### 3. 鲁棒性设计
- **角度跳变处理**: 处理359°→1°的角度跳变
- **阈值过滤**: 小于0.1°的变化不执行控制，避免抖动
- **状态保护**: 首次运行和状态切换的保护机制

## 应用场景

这个系统特别适用于需要**高精度激光跟踪**的场景，如：
- 激光雕刻/切割中的路径跟踪
- 激光测距中的目标锁定
- 激光通信中的光束对准
- 竞赛中的激光打靶系统

## 总结

`task4_proc2()` 实现了一个**智能混合控制系统**，通过视觉跟踪和IMU四拐点补偿的有机结合，在保证控制精度的同时提供了优秀的动态响应性能。这是一个在嵌入式控制领域具有创新性的解决方案。
