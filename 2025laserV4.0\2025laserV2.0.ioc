#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=UART4_RX
Dma.RequestsNb=1
Dma.UART4_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART4_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART4_RX.0.Instance=DMA1_Stream2
Dma.UART4_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART4_RX.0.MemInc=DMA_MINC_ENABLE
Dma.UART4_RX.0.Mode=DMA_NORMAL
Dma.UART4_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART4_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.UART4_RX.0.Priority=DMA_PRIORITY_LOW
Dma.UART4_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=
KeepUserPlacement=false
Mcu.CPN=STM32F407VET6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=NVIC
Mcu.IP10=USART6
Mcu.IP2=RCC
Mcu.IP3=SPI1
Mcu.IP4=SYS
Mcu.IP5=TIM6
Mcu.IP6=UART4
Mcu.IP7=USART1
Mcu.IP8=USART2
Mcu.IP9=USART3
Mcu.IPNb=11
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PH0-OSC_IN
Mcu.Pin1=PH1-OSC_OUT
Mcu.Pin10=PA9
Mcu.Pin11=PA10
Mcu.Pin12=PA13
Mcu.Pin13=PA14
Mcu.Pin14=PC10
Mcu.Pin15=PC11
Mcu.Pin16=PD0
Mcu.Pin17=PD1
Mcu.Pin18=PD4
Mcu.Pin19=PD5
Mcu.Pin2=PA4
Mcu.Pin20=PD6
Mcu.Pin21=PE1
Mcu.Pin22=VP_SYS_VS_Systick
Mcu.Pin23=VP_TIM6_VS_ClockSourceINT
Mcu.Pin3=PA5
Mcu.Pin4=PA6
Mcu.Pin5=PA7
Mcu.Pin6=PB10
Mcu.Pin7=PB11
Mcu.Pin8=PC6
Mcu.Pin9=PC7
Mcu.PinsNb=24
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VETx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM6_DAC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UART4_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART6_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA4.GPIOParameters=PinState,GPIO_Label
PA4.GPIO_Label=W25Q64_CHIP_SELECT
PA4.Locked=true
PA4.PinState=GPIO_PIN_SET
PA4.Signal=GPIO_Output
PA5.Mode=Full_Duplex_Master
PA5.Signal=SPI1_SCK
PA6.Mode=Full_Duplex_Master
PA6.Signal=SPI1_MISO
PA7.Mode=Full_Duplex_Master
PA7.Signal=SPI1_MOSI
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PC10.Locked=true
PC10.Mode=Asynchronous
PC10.Signal=UART4_TX
PC11.Locked=true
PC11.Mode=Asynchronous
PC11.Signal=UART4_RX
PC6.Mode=Asynchronous
PC6.Signal=USART6_TX
PC7.Mode=Asynchronous
PC7.Signal=USART6_RX
PD0.GPIOParameters=GPIO_PuPd,GPIO_Label
PD0.GPIO_Label=KEY2
PD0.GPIO_PuPd=GPIO_PULLUP
PD0.Locked=true
PD0.Signal=GPIO_Input
PD1.GPIOParameters=GPIO_PuPd,GPIO_Label
PD1.GPIO_Label=KEY3
PD1.GPIO_PuPd=GPIO_PULLUP
PD1.Locked=true
PD1.Signal=GPIO_Input
PD4.GPIOParameters=GPIO_PuPd,GPIO_Label
PD4.GPIO_Label=KEY1
PD4.GPIO_PuPd=GPIO_PULLUP
PD4.Locked=true
PD4.Signal=GPIO_Input
PD5.Locked=true
PD5.Mode=Asynchronous
PD5.Signal=USART2_TX
PD6.Locked=true
PD6.Mode=Asynchronous
PD6.Signal=USART2_RX
PE1.GPIOParameters=GPIO_Label
PE1.GPIO_Label=RELAY
PE1.Locked=true
PE1.Signal=GPIO_Output
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VETx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=2025laserV2.0.ioc
ProjectManager.ProjectName=2025laserV2.0
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART2_UART_Init-USART2-false-HAL-true,5-MX_USART3_UART_Init-USART3-false-HAL-true,6-MX_USART1_UART_Init-USART1-false-HAL-true,7-MX_TIM6_Init-TIM6-false-HAL-true,8-MX_USART6_UART_Init-USART6-false-HAL-true,9-MX_SPI1_Init-SPI1-false-HAL-true,10-MX_UART4_Init-UART4-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FLatency=FLASH_LATENCY_5
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FLatency,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=8
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_4
SPI1.CalculateBaudRate=21.0 MBits/s
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
TIM6.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM6.CounterMode=TIM_COUNTERMODE_UP
TIM6.IPParameters=Prescaler,CounterMode,Period,AutoReloadPreload,TIM_MasterOutputTrigger
TIM6.Period=200-1
TIM6.Prescaler=8400-1
TIM6.TIM_MasterOutputTrigger=TIM_TRGO_RESET
UART4.IPParameters=VirtualMode
UART4.VirtualMode=Asynchronous
USART1.BaudRate=115200
USART1.IPParameters=BaudRate,WordLength,Parity,StopBits,Mode,OverSampling,VirtualMode
USART1.Mode=MODE_TX_RX
USART1.OverSampling=UART_OVERSAMPLING_16
USART1.Parity=PARITY_NONE
USART1.StopBits=STOPBITS_1
USART1.VirtualMode=VM_ASYNC
USART1.WordLength=WORDLENGTH_8B
USART2.BaudRate=115200
USART2.IPParameters=BaudRate,WordLength,Parity,StopBits,Mode,OverSampling,VirtualMode
USART2.Mode=MODE_TX_RX
USART2.OverSampling=UART_OVERSAMPLING_16
USART2.Parity=PARITY_NONE
USART2.StopBits=STOPBITS_1
USART2.VirtualMode=VM_ASYNC
USART2.WordLength=WORDLENGTH_8B
USART3.BaudRate=115200
USART3.IPParameters=BaudRate,WordLength,Parity,StopBits,Mode,OverSampling,VirtualMode
USART3.Mode=MODE_TX_RX
USART3.OverSampling=UART_OVERSAMPLING_16
USART3.Parity=PARITY_NONE
USART3.StopBits=STOPBITS_1
USART3.VirtualMode=VM_ASYNC
USART3.WordLength=WORDLENGTH_8B
USART6.BaudRate=115200
USART6.IPParameters=BaudRate,WordLength,Parity,StopBits,Mode,OverSampling,VirtualMode
USART6.Mode=MODE_TX_RX
USART6.OverSampling=UART_OVERSAMPLING_16
USART6.Parity=PARITY_NONE
USART6.StopBits=STOPBITS_1
USART6.VirtualMode=VM_ASYNC
USART6.WordLength=WORDLENGTH_8B
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
board=custom
